# GG Catalog Database API

A secure and robust ExpressJS REST API for managing a comprehensive product catalog system with MySQL database integration.

## 🚀 Features

- **Product Management**: Complete CRUD operations for products with variants and photos
- **Brand & Category Management**: Organize products by brands and categories
- **Rating System**: Customer reviews and ratings with automatic statistics
- **Web Banners**: Dynamic banner management for promotional content
- **Admin Management**: Secure admin user management
- **Security Features**: Input validation, rate limiting, and SQL injection protection
- **MySQL Integration**: Robust database connection with connection pooling
- **Error Handling**: Comprehensive error handling and validation
- **CORS Support**: Configurable cross-origin resource sharing

## 🔒 Security Features

- **Input Validation**: All endpoints use express-validator for comprehensive input validation
- **Rate Limiting**: Multiple rate limiting strategies to prevent API abuse
- **SQL Injection Protection**: Parameterized queries for all database operations
- **Security Headers**: Helmet.js for setting security-related HTTP headers
- **Data Sanitization**: Automatic trimming and validation of user inputs
- **Error Handling**: Secure error responses that don't expose sensitive information

## 📋 Prerequisites

- Node.js (v14 or higher)
- MySQL Server (v5.7 or higher)
- npm or yarn package manager

## 🛠️ Quick Start

1. **Clone and Install**
   ```bash
   git clone <repository-url>
   cd GGCatalogDatabase
   npm install
   ```

2. **Environment Setup**
   ```bash
   cp .env.example .env
   # Edit .env with your database credentials
   ```

3. **Database Setup**
   ```bash
   # Create database and tables
   mysql -u root -p < database_schema.sql
   ```

4. **Start the Server**
   ```bash
   npm run dev  # Development mode
   npm start    # Production mode
   ```

## 📚 API Documentation

### Base URL
```
http://localhost:3000/api
```

### Rate Limits
- **General API**: 100 requests per 15 minutes
- **Write Operations**: 50 requests per 15 minutes
- **Admin Operations**: 10 requests per 15 minutes
- **Rating Submissions**: 5 requests per hour
- **Database Tests**: 10 requests per 5 minutes

### Main Endpoints

#### System
- `GET /api/health` - Health check
- `GET /api/test-db` - Database connection test

#### Products
- `GET /api/products` - Get all products
- `GET /api/products/:id` - Get product by ID
- `POST /api/products` - Create product
- `PUT /api/products/:id` - Update product
- `DELETE /api/products/:id` - Delete product

#### Brands
- `GET /api/brands` - Get all brands
- `GET /api/brands/:id` - Get brand by ID
- `POST /api/brands` - Create brand
- `PUT /api/brands/:id` - Update brand
- `DELETE /api/brands/:id` - Delete brand

#### Categories
- `GET /api/categories` - Get all categories
- `GET /api/categories/:id` - Get category by ID
- `POST /api/categories` - Create category
- `PUT /api/categories/:id` - Update category
- `DELETE /api/categories/:id` - Delete category

#### Ratings
- `GET /api/products/:productId/ratings` - Get product ratings
- `POST /api/products/:productId/ratings` - Add rating

#### Web Banners
- `GET /api/banners` - Get all banners
- `GET /api/banners/active` - Get active banners
- `POST /api/banners` - Create banner
- `PUT /api/banners/:id` - Update banner
- `DELETE /api/banners/:id` - Delete banner

## 🛡️ Security Implementation

### Input Validation
All endpoints implement comprehensive validation using express-validator:
- **Data Type Validation**: Ensures correct data types for all inputs
- **Length Validation**: Enforces minimum and maximum length constraints
- **Format Validation**: Validates URLs, email formats, and special patterns
- **Sanitization**: Automatic trimming and cleaning of user inputs

### Rate Limiting Strategy
- **Tiered Approach**: Different limits for different operation types
- **IP-based**: Limits applied per IP address
- **Sliding Window**: 15-minute rolling windows for most operations
- **Graceful Degradation**: Clear error messages when limits are exceeded

### SQL Injection Prevention
- **Parameterized Queries**: All database queries use parameter binding
- **Input Sanitization**: Additional validation before database operations
- **Error Handling**: Database errors don't expose sensitive information

## 📝 Response Format

### Success Response
```json
{
  "success": true,
  "data": {...},
  "message": "Optional success message"
}
```

### Error Response
```json
{
  "success": false,
  "error": "Error description",
  "details": [...] // Validation errors when applicable
}
```

## 🚀 Production Deployment

### Environment Variables
```env
NODE_ENV=production
PORT=3000
DB_HOST=your-db-host
DB_USER=your-db-user
DB_PASSWORD=your-secure-password
DB_NAME=gg_catalog_db
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
```

### Security Checklist
- [ ] Use HTTPS in production
- [ ] Set strong database passwords
- [ ] Configure CORS for specific domains
- [ ] Enable database SSL connections
- [ ] Set up proper logging and monitoring
- [ ] Regular security updates

## 📞 Support

For API documentation, issues, or questions:
- Check the response messages for detailed error information
- Verify all required fields are provided in requests
- Ensure rate limits are not exceeded
- Check server logs for detailed error traces

## 📄 License

This project is licensed under the ISC License.