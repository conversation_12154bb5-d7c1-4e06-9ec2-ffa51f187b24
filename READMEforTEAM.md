# GG Catalog Database API

A comprehensive ExpressJS REST API for managing a product catalog system with MySQL database integration.

## 🚀 Features

- **Product Management**: Complete CRUD operations for products with variants and photos
- **Brand & Category Management**: Organize products by brands and categories
- **Rating System**: Customer reviews and ratings with automatic statistics
- **Web Banners**: Dynamic banner management for promotional content
- **Admin Management**: Basic admin user management
- **MySQL Integration**: Robust database connection with connection pooling
- **Error Handling**: Comprehensive error handling and validation
- **CORS Support**: Cross-origin resource sharing enabled

## 📋 Prerequisites

- Node.js (v14 or higher)
- MySQL Server
- npm or yarn package manager

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd GGCatalogDatabase
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   Create a `.env` file in the root directory:
   ```env
   PORT=3000
   DB_HOST=localhost
   DB_USER=root
   DB_PASSWORD=your_password
   DB_NAME=gg_catalog_db
   ```

4. **Database Setup**
   Create the MySQL database and tables using the schema below.

5. **Start the server**
   ```bash
   # Development mode with nodemon
   npm run dev

   # Production mode
   npm start
   ```

## 🗄️ Database Schema

### Table: admins
```sql
CREATE TABLE admins (
  id INT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### Table: brands
```sql
CREATE TABLE brands (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL UNIQUE,
  brand_photo TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### Table: categories
```sql
CREATE TABLE categories (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL UNIQUE,
  category_photo TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### Table: products
```sql
CREATE TABLE products (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  brand_id INT,
  category_id INT,
  total_sold INT DEFAULT 0,
  avg_rating FLOAT DEFAULT 0,
  total_raters INT DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (brand_id) REFERENCES brands(id) ON DELETE SET NULL,
  FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
);
```

### Table: product_variants
```sql
CREATE TABLE product_variants (
  id INT PRIMARY KEY AUTO_INCREMENT,
  product_id INT,
  variant_name VARCHAR(100),
  price DECIMAL(12,2) NOT NULL,
  FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);
```

### Table: product_photos
```sql
CREATE TABLE product_photos (
  id INT PRIMARY KEY AUTO_INCREMENT,
  product_id INT,
  photo_url TEXT,
  FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);
```

### Table: ratings
```sql
CREATE TABLE ratings (
  id INT PRIMARY KEY AUTO_INCREMENT,
  product_id INT,
  star INT CHECK (star >= 1 AND star <= 5),
  review_text TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);
```

### Table: web_banners
```sql
CREATE TABLE web_banners (
  id INT PRIMARY KEY AUTO_INCREMENT,
  title VARCHAR(255),
  banner_image_url TEXT,
  redirect_url TEXT,
  active BOOLEAN DEFAULT TRUE,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 📚 API Endpoints

### Base URL
```
http://localhost:3000/api
```

### 🔍 System Endpoints

#### Health Check
- **GET** `/health`
  - **Description**: Check API server status
  - **Response**:
    ```json
    {
      "status": "OK",
      "timestamp": "2024-01-01T00:00:00.000Z",
      "service": "GG Catalog API"
    }
    ```

#### Database Test
- **GET** `/test-db`
  - **Description**: Test database connection
  - **Response**:
    ```json
    {
      "message": "Database connection successful",
      "data": [{"test": 1}]
    }
    ```

### 👤 Admin Endpoints

#### Get All Admins
- **GET** `/admins`
  - **Description**: Retrieve all admin users (passwords excluded)
  - **Response**:
    ```json
    {
      "success": true,
      "data": [
        {
          "id": 1,
          "username": "admin1",
          "created_at": "2024-01-01T00:00:00.000Z",
          "updated_at": "2024-01-01T00:00:00.000Z"
        }
      ]
    }
    ```

#### Create Admin
- **POST** `/admins`
  - **Description**: Create a new admin user
  - **Body**:
    ```json
    {
      "username": "newadmin",
      "password_hash": "hashed_password_here"
    }
    ```
  - **Response**:
    ```json
    {
      "success": true,
      "id": 2,
      "message": "Admin created successfully"
    }
    ```

### 🏷️ Brand Endpoints

#### Get All Brands
- **GET** `/brands`
  - **Description**: Retrieve all brands
  - **Response**:
    ```json
    {
      "success": true,
      "data": [
        {
          "id": 1,
          "name": "Nike",
          "brand_photo": "https://example.com/nike-logo.jpg",
          "created_at": "2024-01-01T00:00:00.000Z"
        }
      ]
    }
    ```

#### Get Brand by ID
- **GET** `/brands/:id`
  - **Description**: Retrieve a specific brand
  - **Parameters**: `id` (integer) - Brand ID
  - **Response**:
    ```json
    {
      "success": true,
      "data": {
        "id": 1,
        "name": "Nike",
        "brand_photo": "https://example.com/nike-logo.jpg",
        "created_at": "2024-01-01T00:00:00.000Z"
      }
    }
    ```

#### Create Brand
- **POST** `/brands`
  - **Description**: Create a new brand
  - **Body**:
    ```json
    {
      "name": "Adidas",
      "brand_photo": "https://example.com/adidas-logo.jpg"
    }
    ```
  - **Response**:
    ```json
    {
      "success": true,
      "id": 2,
      "message": "Brand created successfully"
    }
    ```

#### Update Brand
- **PUT** `/brands/:id`
  - **Description**: Update an existing brand
  - **Parameters**: `id` (integer) - Brand ID
  - **Body**:
    ```json
    {
      "name": "Updated Brand Name",
      "brand_photo": "https://example.com/updated-logo.jpg"
    }
    ```
  - **Response**:
    ```json
    {
      "success": true,
      "message": "Brand updated successfully"
    }
    ```

#### Delete Brand
- **DELETE** `/brands/:id`
  - **Description**: Delete a brand
  - **Parameters**: `id` (integer) - Brand ID
  - **Response**:
    ```json
    {
      "success": true,
      "message": "Brand deleted successfully"
    }
    ```

### 📂 Category Endpoints

#### Get All Categories
- **GET** `/categories`
  - **Description**: Retrieve all categories
  - **Response**:
    ```json
    {
      "success": true,
      "data": [
        {
          "id": 1,
          "name": "Electronics",
          "category_photo": "https://example.com/electronics.jpg",
          "created_at": "2024-01-01T00:00:00.000Z"
        }
      ]
    }
    ```

#### Get Category by ID
- **GET** `/categories/:id`
  - **Description**: Retrieve a specific category
  - **Parameters**: `id` (integer) - Category ID

#### Create Category
- **POST** `/categories`
  - **Description**: Create a new category
  - **Body**:
    ```json
    {
      "name": "Sports",
      "category_photo": "https://example.com/sports.jpg"
    }
    ```

#### Update Category
- **PUT** `/categories/:id`
  - **Description**: Update an existing category
  - **Parameters**: `id` (integer) - Category ID

#### Delete Category
- **DELETE** `/categories/:id`
  - **Description**: Delete a category
  - **Parameters**: `id` (integer) - Category ID

### 📦 Product Endpoints

#### Get All Products
- **GET** `/products`
  - **Description**: Retrieve all products with brand and category information
  - **Response**:
    ```json
    {
      "success": true,
      "data": [
        {
          "id": 1,
          "name": "Nike Air Max",
          "description": "Comfortable running shoes",
          "brand_id": 1,
          "category_id": 2,
          "total_sold": 150,
          "avg_rating": 4.5,
          "total_raters": 30,
          "brand_name": "Nike",
          "category_name": "Footwear",
          "created_at": "2024-01-01T00:00:00.000Z",
          "updated_at": "2024-01-01T00:00:00.000Z"
        }
      ]
    }
    ```

#### Get Product by ID
- **GET** `/products/:id`
  - **Description**: Retrieve a specific product with variants and photos
  - **Parameters**: `id` (integer) - Product ID
  - **Response**:
    ```json
    {
      "success": true,
      "data": {
        "id": 1,
        "name": "Nike Air Max",
        "description": "Comfortable running shoes",
        "brand_id": 1,
        "category_id": 2,
        "brand_name": "Nike",
        "category_name": "Footwear",
        "variants": [
          {
            "id": 1,
            "product_id": 1,
            "variant_name": "Size 42",
            "price": 129.99
          }
        ],
        "photos": [
          {
            "id": 1,
            "product_id": 1,
            "photo_url": "https://example.com/shoe1.jpg"
          }
        ]
      }
    }
    ```

#### Create Product
- **POST** `/products`
  - **Description**: Create a new product
  - **Body**:
    ```json
    {
      "name": "New Product",
      "description": "Product description",
      "brand_id": 1,
      "category_id": 2
    }
    ```

#### Update Product
- **PUT** `/products/:id`
  - **Description**: Update an existing product
  - **Parameters**: `id` (integer) - Product ID

#### Delete Product
- **DELETE** `/products/:id`
  - **Description**: Delete a product
  - **Parameters**: `id` (integer) - Product ID

### 🔧 Product Variant Endpoints

#### Get Product Variants
- **GET** `/products/:productId/variants`
  - **Description**: Get all variants for a specific product
  - **Parameters**: `productId` (integer) - Product ID
  - **Response**:
    ```json
    {
      "success": true,
      "data": [
        {
          "id": 1,
          "product_id": 1,
          "variant_name": "Size 42",
          "price": 129.99
        }
      ]
    }
    ```

#### Add Product Variant
- **POST** `/products/:productId/variants`
  - **Description**: Add a new variant to a product
  - **Parameters**: `productId` (integer) - Product ID
  - **Body**:
    ```json
    {
      "variant_name": "Size 44",
      "price": 129.99
    }
    ```

#### Update Variant
- **PUT** `/variants/:id`
  - **Description**: Update a product variant
  - **Parameters**: `id` (integer) - Variant ID
  - **Body**:
    ```json
    {
      "variant_name": "Size 44 - Black",
      "price": 139.99
    }
    ```

#### Delete Variant
- **DELETE** `/variants/:id`
  - **Description**: Delete a product variant
  - **Parameters**: `id` (integer) - Variant ID

### 📸 Product Photo Endpoints

#### Get Product Photos
- **GET** `/products/:productId/photos`
  - **Description**: Get all photos for a specific product
  - **Parameters**: `productId` (integer) - Product ID

#### Add Product Photo
- **POST** `/products/:productId/photos`
  - **Description**: Add a new photo to a product
  - **Parameters**: `productId` (integer) - Product ID
  - **Body**:
    ```json
    {
      "photo_url": "https://example.com/product-photo.jpg"
    }
    ```

#### Delete Photo
- **DELETE** `/photos/:id`
  - **Description**: Delete a product photo
  - **Parameters**: `id` (integer) - Photo ID

### ⭐ Rating Endpoints

#### Get Product Ratings
- **GET** `/products/:productId/ratings`
  - **Description**: Get all ratings for a specific product
  - **Parameters**: `productId` (integer) - Product ID
  - **Response**:
    ```json
    {
      "success": true,
      "data": [
        {
          "id": 1,
          "product_id": 1,
          "star": 5,
          "review_text": "Excellent product!",
          "created_at": "2024-01-01T00:00:00.000Z"
        }
      ]
    }
    ```

#### Add Product Rating
- **POST** `/products/:productId/ratings`
  - **Description**: Add a new rating to a product (automatically updates product statistics)
  - **Parameters**: `productId` (integer) - Product ID
  - **Body**:
    ```json
    {
      "star": 5,
      "review_text": "Great product, highly recommended!"
    }
    ```

### 🎯 Web Banner Endpoints

#### Get All Banners
- **GET** `/banners`
  - **Description**: Retrieve all web banners
  - **Response**:
    ```json
    {
      "success": true,
      "data": [
        {
          "id": 1,
          "title": "Summer Sale",
          "banner_image_url": "https://example.com/summer-banner.jpg",
          "redirect_url": "https://example.com/summer-sale",
          "active": true,
          "created_at": "2024-01-01T00:00:00.000Z",
          "updated_at": "2024-01-01T00:00:00.000Z"
        }
      ]
    }
    ```

#### Get Active Banners
- **GET** `/banners/active`
  - **Description**: Retrieve only active web banners
  - **Response**: Same as above, but filtered for active banners only

#### Create Banner
- **POST** `/banners`
  - **Description**: Create a new web banner
  - **Body**:
    ```json
    {
      "title": "New Year Sale",
      "banner_image_url": "https://example.com/newyear-banner.jpg",
      "redirect_url": "https://example.com/newyear-sale",
      "active": true
    }
    ```

#### Update Banner
- **PUT** `/banners/:id`
  - **Description**: Update an existing banner
  - **Parameters**: `id` (integer) - Banner ID
  - **Body**:
    ```json
    {
      "title": "Updated Sale",
      "banner_image_url": "https://example.com/updated-banner.jpg",
      "redirect_url": "https://example.com/updated-sale",
      "active": false
    }
    ```

#### Delete Banner
- **DELETE** `/banners/:id`
  - **Description**: Delete a web banner
  - **Parameters**: `id` (integer) - Banner ID

## 📝 Response Format

All API responses follow a consistent format:

### Success Response
```json
{
  "success": true,
  "data": [...], // or single object
  "message": "Optional success message"
}
```

### Error Response
```json
{
  "success": false,
  "error": "Error message description"
}
```

## 🔧 Development

### Available Scripts

Add these scripts to your `package.json`:

```json
{
  "scripts": {
    "start": "node index.js",
    "dev": "nodemon index.js",
    "test": "echo \"Error: no test specified\" && exit 1"
  }
}
```

### Running the Server

```bash
# Development mode (auto-restart on changes)
npm run dev

# Production mode
npm start
```

### Testing the API

You can test the API using tools like:
- **Postman**: Import the endpoints and test them
- **curl**: Command line testing
- **Thunder Client**: VS Code extension
- **Insomnia**: API testing tool

Example curl command:
```bash
# Test health endpoint
curl http://localhost:3000/api/health

# Get all products
curl http://localhost:3000/api/products

# Create a new brand
curl -X POST http://localhost:3000/api/brands \
  -H "Content-Type: application/json" \
  -d '{"name":"Test Brand","brand_photo":"https://example.com/logo.jpg"}'
```

## 🛡️ Error Handling

The API includes comprehensive error handling:
- **400 Bad Request**: Invalid request data
- **404 Not Found**: Resource not found
- **500 Internal Server Error**: Database or server errors

## 🔒 Security Implementation (IMPLEMENTED)

### ✅ Input Validation (express-validator)
All endpoints now include comprehensive validation:
- **Data Type Validation**: Ensures correct data types for all inputs
- **Length Validation**: Enforces minimum and maximum length constraints
- **Format Validation**: Validates URLs, email formats, and special patterns
- **Sanitization**: Automatic trimming and cleaning of user inputs
- **Custom Validation**: Business logic validation (e.g., star ratings 1-5)

### ✅ Rate Limiting (express-rate-limit)
Implemented tiered rate limiting strategy:
- **General API**: 100 requests per 15 minutes per IP
- **Write Operations (POST/PUT/DELETE)**: 50 requests per 15 minutes per IP
- **Admin Operations**: 10 requests per 15 minutes per IP
- **Rating Submissions**: 5 requests per hour per IP (prevents spam)
- **Database Tests**: 10 requests per 5 minutes per IP

### ✅ Security Headers (helmet)
- **XSS Protection**: Prevents cross-site scripting attacks
- **Content Security Policy**: Controls resource loading
- **HSTS**: Enforces HTTPS connections
- **Frame Options**: Prevents clickjacking
- **Content Type Sniffing**: Prevents MIME type confusion

### ✅ SQL Injection Protection
- **Parameterized Queries**: All database queries use parameter binding
- **Input Sanitization**: Additional validation before database operations
- **Error Handling**: Database errors don't expose sensitive information
- **Duplicate Prevention**: Checks for existing records before creation

### ✅ Enhanced Error Handling
- **Consistent Response Format**: All responses follow standard format
- **Validation Error Details**: Clear validation error messages
- **Rate Limit Information**: Headers indicate remaining requests
- **Secure Error Messages**: No sensitive information in error responses

### ✅ CORS Configuration
- **Environment-based Origins**: Configurable allowed origins
- **Credentials Support**: Proper handling of authentication cookies
- **Preflight Handling**: Automatic OPTIONS request handling

### 🔄 Additional Security Considerations for Production
- **Authentication & Authorization**: JWT tokens, API keys (to be implemented)
- **HTTPS**: Secure data transmission (infrastructure level)
- **Database SSL**: Encrypted database connections
- **Logging & Monitoring**: Request logging and anomaly detection
- **API Versioning**: Version management for breaking changes

## 📊 Database Relationships

- **Products** belong to **Brands** and **Categories**
- **Products** can have multiple **Variants** and **Photos**
- **Products** can have multiple **Ratings**
- **Web Banners** are independent promotional content

## 🚀 Deployment

### Environment Variables for Production
```env
NODE_ENV=production
PORT=3000
DB_HOST=your-production-db-host
DB_USER=your-db-user
DB_PASSWORD=your-secure-password
DB_NAME=gg_catalog_db
```

### PM2 Deployment (Recommended)
```bash
npm install -g pm2
pm2 start index.js --name "gg-catalog-api"
pm2 startup
pm2 save
```

## 📞 Support

For issues and questions:
1. Check the API response messages for detailed error information
2. Verify database connection and table structure
3. Ensure all required fields are provided in requests
4. Check server logs for detailed error traces

## 🔍 Security Audit Report

### Query Security Analysis
All database queries have been reviewed and secured:

#### ✅ Parameterized Queries
```javascript
// SECURE - Uses parameterized queries
const [rows] = await db.execute('SELECT * FROM brands WHERE id = ?', [req.params.id]);

// SECURE - Multiple parameters properly bound
const [result] = await db.execute(
  'INSERT INTO products (name, description, brand_id, category_id) VALUES (?, ?, ?, ?)',
  [name, description, brand_id, category_id]
);
```

#### ✅ Input Validation Examples
```javascript
// Brand validation
const validateBrand = [
  body('name')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Brand name must be between 1 and 100 characters')
    .matches(/^[a-zA-Z0-9\s\-&.]+$/)
    .withMessage('Brand name contains invalid characters'),
  body('brand_photo')
    .optional()
    .isURL()
    .withMessage('Brand photo must be a valid URL')
];

// Rating validation with business logic
const validateRating = [
  body('star')
    .isInt({ min: 1, max: 5 })
    .withMessage('Star rating must be an integer between 1 and 5'),
  body('review_text')
    .optional()
    .trim()
    .isLength({ max: 2000 })
    .withMessage('Review text must not exceed 2000 characters')
];
```

#### ✅ Rate Limiting Configuration
```javascript
// Different limits for different operations
const generalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // 100 requests per window
  message: { success: false, error: 'Too many requests...' }
});

const ratingLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 5, // Only 5 rating submissions per hour
  message: { success: false, error: 'Too many rating submissions...' }
});
```

### Validation Rules Summary

| Endpoint | Validation Rules | Rate Limit |
|----------|------------------|------------|
| `POST /api/admins` | Username (3-50 chars, alphanumeric), Password hash required | Admin (10/15min) |
| `POST /api/brands` | Name (1-100 chars, safe chars), Optional URL validation | Strict (50/15min) |
| `POST /api/categories` | Name (1-100 chars, safe chars), Optional URL validation | Strict (50/15min) |
| `POST /api/products` | Name (1-255 chars), Description (max 5000), Valid IDs | Strict (50/15min) |
| `POST /api/variants` | Name (1-100 chars), Price (positive, 2 decimals) | Strict (50/15min) |
| `POST /api/photos` | Valid image URL with proper extensions | Strict (50/15min) |
| `POST /api/ratings` | Star (1-5 integer), Review (max 2000 chars) | Rating (5/hour) |
| `POST /api/banners` | Optional title (max 255), Valid URLs | Strict (50/15min) |

### Security Testing Checklist

#### ✅ Completed Tests
- [x] SQL Injection attempts blocked by parameterized queries
- [x] XSS attempts sanitized by input validation
- [x] Rate limiting prevents brute force attacks
- [x] Invalid data types rejected with proper error messages
- [x] Oversized payloads rejected (10MB limit)
- [x] Malformed URLs rejected by validation
- [x] Duplicate entries prevented with database checks
- [x] Foreign key constraints validated before operations

#### 🔄 Recommended Additional Tests
- [ ] Load testing with concurrent requests
- [ ] Penetration testing with security tools
- [ ] Authentication bypass testing (when auth is implemented)
- [ ] Session management testing (when sessions are implemented)
- [ ] File upload security testing (if file uploads are added)

## 📄 License

This project is licensed under the ISC License.