# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password_here
DB_NAME=gg_catalog_db

# Optional: Database Connection Pool Settings
DB_CONNECTION_LIMIT=10
DB_QUEUE_LIMIT=0

# Security Configuration
# CORS - Comma-separated list of allowed origins (use * for development only)
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,http://127.0.0.1:3000

# Rate Limiting (optional - defaults are set in code)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_STRICT_MAX=50
RATE_LIMIT_ADMIN_MAX=10

# Request Size Limits
MAX_REQUEST_SIZE=10mb
